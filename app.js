// app.js
const { GiftRecordStorage } = require('./utils/storage')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })

    // 初始化应用数据
    this.initAppData()
  },

  globalData: {
    userInfo: null,
    giftRecords: [], // 存储送礼记录
    appVersion: '1.0.0'
  },

  // 初始化应用数据
  initAppData() {
    // 加载送礼记录到全局数据
    this.globalData.giftRecords = this.getGiftRecords()

    // 检查存储空间
    this.checkStorageSpace()
  },

  // 检查存储空间
  checkStorageSpace() {
    try {
      const info = wx.getStorageInfoSync()
      const usageRatio = info.currentSize / info.limitSize
      if (usageRatio > 0.8) {
        console.warn('存储空间使用率较高:', `${(usageRatio * 100).toFixed(1)}%`)
      }
    } catch (error) {
      console.error('检查存储空间失败:', error)
    }
  },

  // 获取送礼记录
  getGiftRecords() {
    const records = GiftRecordStorage.getRecords()
    this.globalData.giftRecords = records
    return records
  },

  // 保存送礼记录
  saveGiftRecords(records) {
    const success = GiftRecordStorage.saveRecords(records)
    if (success) {
      this.globalData.giftRecords = records
    }
    return success
  },

  // 添加送礼记录
  addGiftRecord(record) {
    const success = GiftRecordStorage.addRecord(record)
    if (success) {
      this.globalData.giftRecords = this.getGiftRecords()
    }
    return success
  },

  // 更新送礼记录
  updateGiftRecord(id, updatedRecord) {
    const success = GiftRecordStorage.updateRecord(id, updatedRecord)
    if (success) {
      this.globalData.giftRecords = this.getGiftRecords()
    }
    return success
  },

  // 删除送礼记录
  deleteGiftRecord(id) {
    const success = GiftRecordStorage.deleteRecord(id)
    if (success) {
      this.globalData.giftRecords = this.getGiftRecords()
    }
    return success
  },

  // 根据ID获取记录
  getGiftRecordById(id) {
    return GiftRecordStorage.getRecordById(id)
  }
})
