# 送礼账单微信小程序

一个用于记录送礼账单的微信小程序，帮助用户管理和追踪送礼记录。

## 功能特性

### 核心功能
- ✅ **账单管理**：添加、编辑、删除送礼记录
- ✅ **详细记录**：记录主人家名字、送礼人信息、地址、金额等
- ✅ **分类管理**：支持不同送礼场合和礼品类型
- ✅ **搜索功能**：快速查找特定的送礼记录
- ✅ **统计功能**：显示总记录数和总金额

### 数据字段
每条送礼记录包含以下信息：
- 主人家名字（必填）
- 送礼人名字（必填）
- 送礼人地址（可选）
- 送礼金额（必填）
- 送礼场合（结婚、生日、满月等）
- 礼品类型（现金、礼品、红包等）
- 事件日期（必填）
- 备注信息（可选）

## 技术架构

### 前端技术
- **框架**：微信小程序原生开发
- **样式**：WXSS + 响应式设计
- **数据存储**：微信小程序本地存储API

### 项目结构
```
├── app.js                 # 应用入口文件
├── app.json              # 应用配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── pages/                # 页面目录
│   ├── index/            # 账单列表页
│   ├── add/              # 添加/编辑账单页
│   └── detail/           # 账单详情页
├── utils/                # 工具函数目录
│   ├── dataModel.js      # 数据模型定义
│   ├── storage.js        # 存储管理器
│   └── common.js         # 通用工具函数
└── images/               # 图片资源目录
```

### 数据存储
- 使用微信小程序的本地存储API
- 支持数据的增删改查操作
- 包含存储空间检查和管理
- 预留云端备份接口

## 页面说明

### 1. 账单列表页 (pages/index)
- 显示所有送礼记录
- 支持搜索功能
- 显示统计信息（总记录数、总金额）
- 支持下拉刷新
- 点击记录可查看详情

### 2. 添加账单页 (pages/add)
- 表单式录入送礼信息
- 支持选择器选择场合和礼品类型
- 表单验证确保数据完整性
- 支持编辑已有记录

### 3. 账单详情页 (pages/detail)
- 显示完整的送礼记录信息
- 支持编辑和删除操作
- 显示创建和更新时间
- 支持分享功能

## 使用说明

### 开发环境搭建
1. 安装微信开发者工具
2. 创建新的小程序项目
3. 将代码复制到项目目录
4. 在开发者工具中打开项目

### 部署说明
1. 在微信公众平台注册小程序账号
2. 获取小程序的AppID
3. 在app.json中配置正确的AppID
4. 上传代码并提交审核

## 数据模型

### GiftRecord 数据结构
```javascript
{
  id: number,              // 记录唯一标识
  hostName: string,        // 主人家名字
  giftGiverName: string,   // 送礼人名字
  giftGiverAddress: string,// 送礼人地址
  amount: number,          // 送礼金额
  occasion: string,        // 送礼场合
  giftType: string,        // 礼品类型
  eventDate: string,       // 事件日期
  note: string,            // 备注信息
  createTime: string,      // 创建时间
  updateTime: string       // 更新时间
}
```

## 扩展功能（待开发）

### 计划中的功能
- [ ] 云端数据同步
- [ ] 数据导出功能（Excel/PDF）
- [ ] 图表统计分析
- [ ] 提醒功能（重要日期提醒）
- [ ] 分类标签管理
- [ ] 数据备份与恢复
- [ ] 多用户支持

### 性能优化
- [ ] 虚拟列表（大数据量优化）
- [ ] 图片懒加载
- [ ] 缓存策略优化
- [ ] 离线数据同步

## 注意事项

1. **数据安全**：所有数据存储在用户本地，请注意备份重要数据
2. **存储限制**：微信小程序本地存储有容量限制，建议定期清理无用数据
3. **兼容性**：支持微信版本7.0.0及以上
4. **隐私保护**：应用不会收集用户个人信息

## 版本历史

### v1.0.0 (当前版本)
- 基础的账单管理功能
- 本地数据存储
- 搜索和统计功能
- 响应式界面设计

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址：[GitHub仓库地址]
- 邮箱：[开发者邮箱]

## 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。
